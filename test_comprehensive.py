#!/usr/bin/env python3
"""
Comprehensive test to verify all endpoints still work correctly after the fix.
"""

import json
import subprocess
import sys

def test_health_endpoint():
    """Test the health endpoint."""
    print("🏥 Testing health endpoint...")
    
    result = subprocess.run([
        'curl', '-s', 'http://localhost:5001/health'
    ], capture_output=True, text=True, timeout=10)
    
    if result.returncode != 0:
        print(f"❌ Health check failed: {result.stderr}")
        return False
    
    try:
        response = json.loads(result.stdout)
        if response.get("status") == "healthy":
            print("✅ Health endpoint working")
            return True
        else:
            print(f"❌ Unexpected health response: {response}")
            return False
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON from health endpoint: {result.stdout}")
        return False

def test_single_text_sanitization():
    """Test single text sanitization."""
    print("📝 Testing single text sanitization...")
    
    test_data = {"text": "This is a test document with some **markdown** formatting."}
    
    result = subprocess.run([
        'curl', '-s', '-X', 'POST',
        'http://localhost:5001/sanitize_texts',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps(test_data)
    ], capture_output=True, text=True, timeout=15)
    
    if result.returncode != 0:
        print(f"❌ Single text test failed: {result.stderr}")
        return False
    
    try:
        response = json.loads(result.stdout)
        if "sanitized_text" in response and response.get("status") == "success":
            print("✅ Single text sanitization working")
            return True
        else:
            print(f"❌ Unexpected single text response: {response}")
            return False
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON from single text endpoint: {result.stdout}")
        return False

def test_dual_source_with_complex_annotation():
    """Test dual source with complex nested annotation data."""
    print("🔄 Testing dual source with complex annotation...")
    
    # More complex annotation with nested structures
    complex_annotation = {
        "Vendor Name": "Complex Corp",
        "Invoice Number": "INV-2025-001",
        "Customer Number": "CUST-12345",
        "Current Charges": "$1,234.56",
        "Finance Charges": "$0.00",
        "Invoice Date": "2025-05-29",
        "Current Charges Due (USD)": "$1,234.56",
        "Services_detail": {
            "Service Charges": "$1,000.00",
            "Usage": "$150.00",
            "Taxes and Surcharges": "$84.56",
            "Service Category": {
                "High-Speed Internet": "$800.00",
                "Premium Support": "$200.00",
                "Static IP Block": "$50.00"
            },
            "Additional_Services": [
                {"name": "Backup Service", "cost": "$25.00"},
                {"name": "Monitoring", "cost": "$25.00"}
            ],
            "Billing_Period": {
                "start_date": "2025-05-01",
                "end_date": "2025-05-31",
                "days": 31
            }
        },
        "Payment_Terms": {
            "due_date": "2025-06-15",
            "late_fee": "$50.00",
            "discount_terms": "2% if paid within 10 days"
        }
    }
    
    test_data = [
        {
            "pages": [
                {
                    "markdown": "# Complex Invoice\n\nVendor: Complex Corp\nInvoice: INV-2025-001"
                }
            ],
            "document_annotation": json.dumps(complex_annotation)
        },
        {
            "original_extracted_text": "Complex PyMuPDF extracted text with multiple lines\nand various formatting.",
            "llm_friendly_markdown_output": {
                "markdown_chunks": ["# Complex markdown", "## Section 2"]
            }
        }
    ]
    
    result = subprocess.run([
        'curl', '-s', '-X', 'POST',
        'http://localhost:5001/sanitize_texts',
        '-H', 'Content-Type: application/json',
        '-d', json.dumps(test_data)
    ], capture_output=True, text=True, timeout=20)
    
    if result.returncode != 0:
        print(f"❌ Complex annotation test failed: {result.stderr}")
        return False
    
    try:
        response = json.loads(result.stdout)
        output_annotation = response.get("mistral_document_annotation")
        
        if not output_annotation:
            print("❌ No annotation in complex test response")
            return False
        
        # Verify complex nested structures are preserved
        if "Services_detail" not in output_annotation:
            print("❌ Services_detail missing from complex annotation")
            return False
        
        services = output_annotation["Services_detail"]
        if "Additional_Services" not in services:
            print("❌ Additional_Services array missing")
            return False
        
        if "Billing_Period" not in services:
            print("❌ Billing_Period object missing")
            return False
        
        if "Payment_Terms" not in output_annotation:
            print("❌ Payment_Terms missing")
            return False
        
        # Check array preservation
        additional_services = services["Additional_Services"]
        if not isinstance(additional_services, list) or len(additional_services) != 2:
            print(f"❌ Additional_Services not preserved correctly: {additional_services}")
            return False
        
        print("✅ Complex nested annotation preserved correctly")
        print(f"📊 Total annotation fields: {len(output_annotation)}")
        print(f"📊 Services_detail fields: {len(services)}")
        print(f"📊 Additional_Services items: {len(additional_services)}")
        
        return True
        
    except json.JSONDecodeError:
        print(f"❌ Invalid JSON from complex test: {result.stdout}")
        return False

def run_all_tests():
    """Run all tests and return overall success."""
    tests = [
        test_health_endpoint,
        test_single_text_sanitization,
        test_dual_source_with_complex_annotation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()  # Add spacing between tests
    
    return all(results)

if __name__ == "__main__":
    print("🧪 Comprehensive API Testing")
    print("=" * 50)
    
    success = run_all_tests()
    
    print("=" * 50)
    if success:
        print("✅ ALL TESTS PASSED: API working correctly after fix!")
    else:
        print("❌ SOME TESTS FAILED: Check the output above")
        
    sys.exit(0 if success else 1)
