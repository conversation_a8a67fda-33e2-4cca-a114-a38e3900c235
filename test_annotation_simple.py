#!/usr/bin/env python3
"""
Simple test to verify that mistral_document_annotation data is preserved.
Uses curl instead of requests to avoid dependency issues.
"""

import json
import subprocess
import sys

def test_annotation_preservation():
    """Test that all fields from the input annotation are preserved in the output."""
    
    # Test data based on the structure we expect
    test_data = [
        {
            "pages": [
                {
                    "markdown": "# Test Invoice\n\nVendor: BCN Telecom\nInvoice: 23918420"
                }
            ],
            "document_annotation": json.dumps({
                "Vendor Name": "BCN Telecom",
                "Invoice Number": "23918420", 
                "Customer Number": "BOC20326",
                "Current Charges": "$442.92",
                "Finance Charges": "$0.00",
                "Invoice Date": "5/1/2025",
                "Current Charges Due (USD)": "$442.92",
                "Services_detail": {
                    "Service Charges": "$366.90",
                    "Usage": "$0.00",
                    "Taxes and Surcharges": "$76.02",
                    "Service Category": {
                        "1.25G X 35M Cable Internet": "$330.95",
                        "5 Static IPs": "$20.00",
                        "Standard Modem Equipment": "$15.95"
                    }
                }
            })
        },
        {
            "original_extracted_text": "Test PyMuPDF text",
            "llm_friendly_markdown_output": {
                "markdown_chunks": ["# Test markdown"]
            }
        }
    ]
    
    # Convert to JSON string for curl
    json_data = json.dumps(test_data)
    
    print("🧪 Testing annotation preservation...")
    print("📤 Sending test data to Flask API...")
    
    try:
        # Use curl to send the request
        curl_command = [
            'curl', '-s', '-X', 'POST',
            'http://localhost:5001/sanitize_texts',
            '-H', 'Content-Type: application/json',
            '-d', json_data
        ]
        
        result = subprocess.run(curl_command, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print(f"❌ Curl command failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
        # Parse the response
        try:
            response_data = json.loads(result.stdout)
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON response: {e}")
            print(f"Raw response: {result.stdout}")
            return False
        
        # Extract the annotation from the response
        output_annotation = response_data.get("mistral_document_annotation")
        
        if not output_annotation:
            print("❌ No mistral_document_annotation found in response")
            print(f"Response keys: {list(response_data.keys())}")
            return False
        
        # Parse the original annotation for comparison
        original_annotation = json.loads(test_data[0]["document_annotation"])
        
        # Compare all fields
        print("🔍 Comparing input vs output annotation...")
        
        missing_fields = []
        for key, value in original_annotation.items():
            if key not in output_annotation:
                missing_fields.append(key)
            elif output_annotation[key] != value:
                print(f"⚠️  Field '{key}' value mismatch:")
                print(f"   Input:  {value}")
                print(f"   Output: {output_annotation[key]}")
        
        if missing_fields:
            print(f"❌ Missing fields in output: {missing_fields}")
            return False
        
        # Check for extra fields (shouldn't happen, but good to verify)
        extra_fields = [key for key in output_annotation.keys() if key not in original_annotation]
        if extra_fields:
            print(f"ℹ️  Extra fields in output (not necessarily bad): {extra_fields}")
        
        print("✅ All annotation fields preserved correctly!")
        print(f"📊 Input fields: {len(original_annotation)}")
        print(f"📊 Output fields: {len(output_annotation)}")
        
        # Show the Services_detail structure to verify nested data
        if "Services_detail" in output_annotation:
            services = output_annotation["Services_detail"]
            print(f"📊 Services_detail has {len(services)} top-level keys")
            if "Service Category" in services:
                print(f"📊 Service Category has {len(services['Service Category'])} items")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔬 Testing Mistral document_annotation field preservation")
    print("=" * 60)
    
    success = test_annotation_preservation()
    
    print("=" * 60)
    if success:
        print("✅ TEST PASSED: Annotation data preserved correctly!")
    else:
        print("❌ TEST FAILED: Issues with annotation processing")
        
    sys.exit(0 if success else 1)
