
  "document_annotation_format": {

        "type": "json_schema",

        "json_schema": {

            "schema": {

                "properties": {

                    "Vendor Name": {"title": "Vendor Name", "type": "string"},

                    "Account/Customer Number": {"title": "Account/Customer Number", "type": "string"},
                    
                    "Invoice Number": {"title": "Invoice Number", "type": "string"},
                    
                    "Bill/Invoice Date": {"title": "Bill/Invoice Date", "type": "string"},
                    
                    "Previous Charges": {"title": "Previous Charges", "type": "string"},

                    "Payment": {"title": "Payment", "type": "string"},

                    "Current Charges": {"title": "Current Charges", "type": "string"},

                    "Finance Charges": {"title": "Finance Charges", "type": "string"},




                    "Service total (USD)": {"title": "Service Total (USD)", "type": "string"},

                    "Tax total (USD)": {"title": "Tax Total (USD)", "type": "string"},

                    "Location Associated with charges" : {"title": "Location Associated", "type": "string"},


                      "Services_detail": {"title": "Services_detail", "type": "object", 
                              
                        "description": "An object for service details. Define specific properties here if needed.(Will never be a location or address) ",

                        "additionalProperties": false
                                          }

                },

                "required": ["Customer Number", "Invoice Number", "Vendor Name"],

                "title": "DocumentAnnotation",

                "type": "object",

                "additionalProperties": false

            },

            "name": "document_annotation",

            "strict": true

        }

    }

